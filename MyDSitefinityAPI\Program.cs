using Microsoft.OpenApi.Models;
using MyDSitefinityAPI.Services;
using System.Reflection;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Core;
using Swashbuckle.AspNetCore.Filters;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.EmailHelpers;
using MyDSitefinityAPI.ConfigHelpers;
using MyDSitefinityAPI.DBContext;
using Mydentist.MyDSitefinityAPI.ImplementationServices;
using Mydentist.MyDSitefinityAPI.Persistence;
using Microsoft.EntityFrameworkCore;
using Mydentist.MyDSitefinityApi.ClinicianPortalApi;
using Mydentist.MyDSitefinityAPI.WebConfigApi;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Services;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Constants;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{

    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Version = "v1",
        Title = "mydentist Sitefinity API",
        Description = "An API for interaction between Sitefinity and the mydentist backend",
   
    });
    options.ExampleFilters();

    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));

});

builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();
builder.Services.AddTransient<ISitefinityFormService, SitefinityFormService>();
builder.Services.AddTransient<IVacancyService, VacancyService>();
builder.Services.AddTransient<IVacancyDbService, VacancyDbService>();
builder.Services.AddTransient<IFindNearestPracticeService, FindNearestPracticeService>();

builder.Services.AddTransient<IEmailSender, EmailSender>();
builder.Services.AddTransient<IViewRender, RazorViewToStringRenderer>();
builder.Services.AddTransient<ILogger, Logger<SitefinityFormService>>();
builder.Services.AddTransient<ILogger, Logger<VacancyService>>();
builder.Services.AddTransient<ILogger, Logger<Email>>();

builder.Services.AddScoped<WebserviceCaller, WebserviceCaller>();
builder.Services.AddScoped<PublicUserAuthenticationService, PublicUserAuthenticationService>();
builder.Services.AddScoped<PerformerProfileService, PerformerProfileService>();

builder.Services.AddScoped<IRecruitmentWebsiteFormImplentationService, RecruitmentWebsiteFormImplentationService>();
builder.Services.AddScoped<IRecruitmentWebsiteFormService, RecruitmentWebsiteFormService>();
builder.Services.AddScoped<IRecruitmentFormTransformerService, RecruitmentFormTransformerService>();
builder.Services.AddScoped<ISharedListMappingService, SharedListMappingService>();
builder.Services.AddScoped<ISharedListService, SharedListService>();

builder.Services.AddScoped<IClinicianPortalApiWrapper, ClinicianPortalApiWrapper>();
builder.Services.AddScoped<IWebsiteConfigRoleService, WebsiteConfigRoleService>();
builder.Services.AddScoped<IWebsiteConfigWrapperService, WebsiteConfigWrapperService>();
builder.Services.AddScoped<IPracticeManagerServices, PracticeManagerService>();

builder.Services.AddScoped<IRecruitmentHubService,RecruitmentHubService>();

builder.Services.Configure<ClinicianPortalSettings>(builder.Configuration.GetSection("ClinicianPortalSettings"));
builder.Services.AddAntiforgery();
builder.Services.AddDbContext<DbContextWarehouse>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DbContextWarehouse")).EnableDetailedErrors());

builder.Services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());
var logOverride = new Dictionary<string, LogLevel>();
logOverride.Add("Microsoft", LogLevel.Warning);
logOverride.Add("Microsoft.Hosting.Lifetime", LogLevel.None);
var loggerFactory = builder.Services.AddLogging(x => x.AddFile(ConfigurationHelper.GetValue("LogLocation"), LogLevel.Information, logOverride));
builder.Services.AddHttpClient(StaticValues.PracticeLocationApi, httpClient =>
{
    httpClient.BaseAddress = new Uri(builder.Configuration.GetSection("NearestPractice:BaseUrl").Value ?? "");
    httpClient.DefaultRequestHeaders.Add(
        "X-ApiKey", builder.Configuration.GetSection("NearestPractice:ApiKey").Value);
    httpClient.DefaultRequestHeaders.Add(
        "Accept", "text/plain");
  
});

var app = builder.Build();

app.UseSwagger();


app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint($"../swagger/v1/swagger.json", "mydentist Sitefinity API");

});


app.UseHttpsRedirection();

app.UseAuthorization();
app.MapControllers();


app.Run();
