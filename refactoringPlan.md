# Refactoring Plan - MyDSitefinityAPI Cleanup

## Current Tasks

### 1. Remove Redundant Controllers and Methods
**Risk Level: Low**
**Status: Pending**

**Current Issue:**
- `PostRecruitmentForm` method duplicates functionality already in main `PostForm` method
- `VacancyController` provides redundant recruitment functionality
- Multiple endpoints for same business logic create confusion and maintenance overhead

**Files to Remove:**
- `MyDSitefinityAPI\Controllers\VacancyController.cs`
- `MyDSitefinityAPI\Controllers\SitefinityFormController.cs` - Remove `PostRecruitmentForm` method only

**Services to Evaluate for Removal:**
- `IVacancyService` and `VacancyService` (if only used by VacancyController)
- `IVacancyDbService` and `VacancyDbService` (check usage in SitefinityFormService)

**Files to Update:**
- `MyDSitefinityAPI\Program.cs` (remove DI registrations for removed services)
- `MyDSitefinityAPI\Services\SitefinityFormService.cs` (remove unused dependencies)

### 2. Dependency Cleanup
**Risk Level: Low**
**Status: Pending**

**Current Issue:**
- Unused EntityFramework NuGet packages in multiple projects

**Files to Update:**
- Remove unused `EntityFramework` NuGet package from `MyDSitefinityAPI.csproj`
- Remove unused `EntityFramework` NuGet package from `MyDSitefinityAPI.Persistence.csproj`

### 3. Security Enhancement
**Risk Level: Medium**
**Status: Pending**

**Current Issue:**
- Hardcoded credentials and URLs in `RecruitmentHubService.cs`

**Files to Update:**
- Move hardcoded credentials and URLs from `RecruitmentHubService.cs` to `appsettings.json`
- Update code to read these values from configuration

### 4. Configuration Modernization
**Risk Level: Medium**
**Status: Pending**

**Current Issue:**
- Using legacy app.config instead of modern appsettings.json for configuration

**Files to Update:**
- Migrate configuration from `app.config` to `appsettings.json`
- Update code to use IConfiguration instead of ConfigurationManager

## Implementation Steps

### Step 1: Remove VacancyController
- Delete entire `MyDSitefinityAPI\Controllers\VacancyController.cs` file

### Step 2: Remove PostRecruitmentForm Method
- Edit `MyDSitefinityAPI\Controllers\SitefinityFormController.cs`
- Remove the `PostRecruitmentForm` method (keep main `PostForm` method)

### Step 3: Clean Up Services
- Check if `IVacancyService`/`VacancyService` are only used by VacancyController
- Check if `IVacancyDbService`/`VacancyDbService` are used in SitefinityFormService
- Remove unused service registrations from `Program.cs`

### Step 4: Update Dependencies
- Remove unused service dependencies from SitefinityFormService constructor

### Step 5: Remove Unused NuGet Packages
- Remove EntityFramework package from both projects

### Step 6: Security Configuration
- Move hardcoded values to appsettings.json
- Update RecruitmentHubService to use IConfiguration

### Step 7: Configuration Migration
- Migrate app.config settings to appsettings.json
- Update all services to use IConfiguration

## Verification Steps
- [ ] Confirm `sf_recruitmentjobapplication` still works through main `PostForm` endpoint
- [ ] Ensure no external systems call the removed endpoints
- [ ] Run integration tests
- [ ] Verify all configuration values are properly migrated
- [ ] Test security enhancements don't break functionality

## Completed Tasks
(None yet)

## Notes
- Main `PostForm` method already handles `sf_recruitmentjobapplication` forms via case statement
- Need to verify if `IVacancyDbService` is used elsewhere before removing
- The JSON sample shows `sf_recruitmentjobapplication` form structure - this should continue working through main endpoint
- Follow SOLID principles and existing project conventions
- Compile and test after each step

**Test Coverage Verification (BEFORE making changes):**
- [x] Created test for main `PostForm` method with `sf_recruitmentjobapplication` payload
- [ ] Run new test to verify recruitment form processing works through main endpoint
- [ ] Run full test suite to establish baseline
- [ ] Document current test coverage metrics
- [ ] Verify `VacancyControllerTests.ApplyToVacancy` functionality is covered by new test

**Test Files to Remove:**
- `MyDSitefinityAPI.IntegrationTests\Tests\Controllers\VacancyControllerTests.cs` (entire file)
- Any unit tests for `VacancyService` and `VacancyDbService` (if they exist)
- Any tests specifically for `PostRecruitmentForm` method

**New Test Files Created:**
- Added `PostRecruitmentJobApplicationForm` test to `SitefinityFormControllerTests.cs`
- Created `GoldenMasters/RecruitmentJobApplication.json` with real recruitment payload

