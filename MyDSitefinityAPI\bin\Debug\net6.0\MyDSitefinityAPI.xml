<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MyDSitefinityAPI</name>
    </assembly>
    <members>
        <member name="M:MyDSitefinityAPI.Controllers.ClinicianApprovalController.Login(System.Guid,System.String)">
            <summary>
            Displays a login form for the current user
            </summary>
            <param name="imageId">The ID of the image the user wants to approve</param>
            <param name="token">A salted username</param>
            <returns></returns>
        </member>
        <member name="M:MyDSitefinityAPI.Controllers.ClinicianApprovalController.Login(MyDSitefinityAPI.Models.PublicUser)">
            <summary>
            Authenticates a user with a username and password
            </summary>
            <param name="user">A PublicUser object representing the current user</param>
            <returns></returns>
        </member>
        <member name="M:MyDSitefinityAPI.Controllers.SitefinityFormController.PostForm(System.String,System.Text.Json.JsonElement)">
             <summary>
             Sends a sitefinity form to the mydentist backend.
             </summary>
             <remarks>
             Sample request:
            
             POST /Sitefinityform
               {
              "Name": "Form is submitted",
              "SiteId": "75c4d617-8e6d-4716-b9eb-a9ef6f2fc2ae",
              "Item": null,
              "OriginalEvent": {
                "EntryId": "cbc0d3b7-1018-475f-be5c-896d5b925c5d",
                "ReferralCode": "1",
                "UserId": "c824f8d5-83fe-478e-8ed0-ade5c30bc884",
                "Username": "<EMAIL>",
                "IpAddress": "127.0.0.1",
                "SubmissionTime": "2022-09-02T10:47:31.3136116Z",
                "FormId": "9289ac17-1a2f-4251-9c11-0ec298b8c45e",
                "FormName": "sf_quickcontactpmform",
                "FormTitle": "TestForm",
                "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
                "SendConfirmationEmail": false,
                "Controls": [
                  {
                    "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox_C004",
                    "Value": "<EMAIL>",
                    "OldValue": null
                  },
                  {
                    "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox_C003",
                    "Value": "07777777779",
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox_C001",
                    "Value": null,
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox",
                    "Value": "General_Enquiry",
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "HiddenPracticeId",
                    "Value": "240=>{my}dentist, Queensway, Bognor Regis",
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox_C011",
                    "Value": "Tambo 2",
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox_C010",
                    "Value": "Another test",
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "FormTextBox_C015",
                    "Value": "20/07/2003",
                    "OldValue": null
                  },
                  {
                "FieldControlName": null,
                    "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
                    "SiblingId": "00000000-0000-0000-0000-000000000000",
                    "Text": null,
                    "Type": 0,
                    "Title": "Random",
                    "FieldName": "GAClientId",
                    "Value": "1373058472.1613488417",
                    "OldValue": null
                  }
                ]
              }
            }
            
             </remarks>
             <response code="201">Form Created Successfully</response>
             <response code="500">Form is not created</response>
             <response code="400">Bad JSON formatting</response>
        </member>
        <member name="M:MyDSitefinityAPI.Controllers.VacancyController.ApplyToVacancy(System.String,System.Text.Json.JsonElement)">
            <summary>
            Applies to a vacancy
            </summary>
            <response code="201">Application created Successfully</response>
            <response code="500">Server Error</response>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PracticeDetails.PerformerImages">
            <summary>
            A lazy-loaded collection of performer images which originated from the current practice
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PracticeDirectory.Performer.PerformerProfile">
            <summary>
            A lazy loaded public profile for the current performer
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PracticeDirectory.PerformerImage.PerformerProfile">
            <summary>
            A lazy loaded instance of the performer profile associated with the image
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PracticeDirectory.PerformerImage.Practice">
            <summary>
            A lazy loaded instance of the originating practice for the image
            </summary>
        </member>
        <member name="T:MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile">
            <summary>
            Represents the public details set for staff member on practice directory
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile.Token">
            <summary>
            The token of the user who's currently approving or rejecting the clinician
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PublicUser.Id">
            <summary>
            The unique record ID for the user
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PublicUser.UserId">
            <summary>
            The username of the user
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PublicUser.Password">
            <summary>
            The password of the user (salted if fetched from the database)
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PublicUser.Salt">
            <summary>
            The salt for the current user
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PublicUser.IsDeleted">
            <summary>
            Whether the current user is deleted
            </summary>
        </member>
        <member name="P:MyDSitefinityAPI.Models.PublicUser.KeyParameter">
            <summary>
            The image id we're approving
            </summary>
        </member>
        <member name="T:MyDSitefinityAPI.Services.PerformerProfileService">
            <summary>
            A service which allows a performer profile to be retrieved and modified
            </summary>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.#ctor(Microsoft.Extensions.Logging.ILogger{MyDSitefinityAPI.Services.PerformerProfileService},MyDSitefinityAPI.DBContext.DBWarehouseContext,MyDSitefinityAPI.Interfaces.IEmailSender,IDHGroup.SharedLibraries.SitefinityServiceCaller.Core.WebserviceCaller)">
            <summary>
            Creates a new <see cref="T:MyDSitefinityAPI.Services.PerformerProfileService"/>
            </summary>
            <param name="logger">A logger for the service</param>
            <param name="dbContext">A DBContext. If null is passed, a new <see cref="T:MyDSitefinityAPI.DBContext.DBWarehouseContext"/> will be created</param>
            <param name="emailSender">An email sender to let appropriate parties know about modifications</param>
            <param name="webserviceCaller">A webservice caller which will send a request to Sitefiinty, to update any approved clinicians</param>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.#ctor(Microsoft.Extensions.Logging.ILogger{MyDSitefinityAPI.Services.PerformerProfileService},MyDSitefinityAPI.Interfaces.IEmailSender,IDHGroup.SharedLibraries.SitefinityServiceCaller.Core.WebserviceCaller)">
            <summary>
            Creates a new <see cref="T:MyDSitefinityAPI.Services.PerformerProfileService"/>
            </summary>
            <param name="logger">A logger for the service</param>
            <param name="emailSender">An email sender to let appropriate parties know about modifications</param>
            <param name="webserviceCaller">A webservice caller which will send a request to Sitefiinty, to update any approved clinicians</param>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.GetByImageId(System.Guid)">
            <summary>
            Gets the <see cref="T:MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile"/> associated with the image ID
            </summary>
            <param name="imageId"></param>
            <returns></returns>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.ApprovePerformer(MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile,MyDSitefinityAPI.Models.PublicUser)">
            <summary>
            Approves a performer and saves any changes to their biography. Resets the rejection note back to null
            </summary>
            <param name="performer"></param>
            <param name="saltedUser">The current user</param>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.RejectPerformer(MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile,MyDSitefinityAPI.Models.PublicUser)">
            <summary>
            Rejects a performer and saves any changes to their biography
            </summary>
            <param name="performer"></param>
            <param name="saltedUser"></param>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.NotifyPracticeOfApprovalAsync(MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile)">
            <summary>
            Lets the practice know that their performer has been approved
            </summary>
            <param name="approvedPerformer"></param>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.NotifyPracticeOfRejectionAsync(MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile)">
            <summary>
            Lets the practice know that their performer has been rejected
            </summary>
            <param name="rejectedPerformer"></param>
            <returns></returns>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.NotifyMarketingOfApprovalAsync(MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile)">
            <summary>
            Lets marketing know that a practice's performer has been approved so they can beautify the image
            </summary>
            <param name="approvedPerformer"></param>
            <returns></returns>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PerformerProfileService.UpdateSitefinityClinician(MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile,System.Int32)">
            <summary>
            Sends a request to Sitefinity to update a clinician
            </summary>
            <param name="performerProfile"></param>
            <param name="practiceId"></param>
        </member>
        <member name="T:MyDSitefinityAPI.Services.PublicUserAuthenticationService">
            <summary>
            An authentication service which allows a request to pass a username and password, which will be authenticated against the GroupWebsitePracticeData records
            </summary>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PublicUserAuthenticationService.#ctor(Microsoft.Extensions.Logging.ILogger{MyDSitefinityAPI.Services.PublicUserAuthenticationService},MyDSitefinityAPI.DBContext.GroupWebsitePracticeDataContext)">
            <summary>
            Creates a new <see cref="T:MyDSitefinityAPI.Services.PublicUserAuthenticationService"/>
            </summary>
            <param name="logger">A logger for the service</param>
            <param name="dbContext">A DBContext. If null is passed, a new <see cref="T:MyDSitefinityAPI.DBContext.GroupWebsitePracticeDataContext"/> will be created</param>
        </member>
        <member name="M:MyDSitefinityAPI.Services.PublicUserAuthenticationService.Authenticate(MyDSitefinityAPI.Models.PublicUser,System.String)">
            <summary>
            Checks whether a password can be used to authenticate against a single user
            </summary>
            <param name="user">The user which we're authenticating</param>
            <param name="password">The password which we're using to prove our identity</param>
            <returns></returns>
        </member>
        <member name="F:MyDSitefinityAPI.Services.PublicUserAuthenticationService.Entropy">
            <summary>
            Added entropy for hashing the user's ID and salt
            </summary>
        </member>
        <member name="T:MyDSitefinity.ResultViewModel">
            <summary>
                A result which is passed as a response which represents whether the request succeeded or failed
            </summary>
        </member>
        <member name="P:MyDSitefinity.ResultViewModel.Value">
            <summary>
                A success message or other object which indicates success
            </summary>
        </member>
        <member name="P:MyDSitefinity.ResultViewModel.Error">
            <summary>
                An error message which represents what went wrong
            </summary>
        </member>
        <member name="M:MyDSitefinity.ResultViewModel.Success(System.Object)">
            <summary>
                Returns a result which represents a successful request
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:MyDSitefinity.ResultViewModel.Failure(System.String)">
            <summary>
                Returns a result which represents a failed request
            </summary>
            <param name="error"></param>
            <returns></returns>
        </member>
    </members>
</doc>
