﻿using MyDSitefinityAPI.ConfigHelpers;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using Newtonsoft.Json;

namespace MyDSitefinityAPI.Services
{
    public class PracticeServices: IPracticeServices
    {
        private static string PracticeApi = $"{ConfigurationHelper.GetValue("PracticeApi")}/api/Practices";      

        private static HttpClient HttpClient = GenerateHttpClient();

        private static HttpClient GenerateHttpClient()
        {
            HttpClient httpClient = new HttpClient();

            if (!httpClient.DefaultRequestHeaders.Contains("APIKey"))
            {
                httpClient.DefaultRequestHeaders.Add("APIKey", ConfigurationHelper.GetValue("PracticeApiKey"));
            }

            httpClient.BaseAddress = new Uri(PracticeApi);

            return httpClient;
        }

        public async Task<PracticeEmail> GetPracticeEmailAsync(string practiceId, string type)
        {
            PracticeEmail practiceEmail = await GetPracticeApiResultAsync<PracticeEmail>($"{PracticeApi}/{practiceId}/Email?type={type}").ConfigureAwait(false);
            return practiceEmail;
        }

        private async Task<TResult> GetPracticeApiResultAsync<TResult>(string path)
        {
            HttpResponseMessage response = await HttpClient.GetAsync(path).ConfigureAwait(false);
            string responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false); // Reading the responses body before checking the success code allows us to read potential error messages
            response.EnsureSuccessStatusCode();

            var practiceList = JsonConvert.DeserializeObject<TResult>(responseBody);
            return practiceList;
        }

    }
}
