using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinityAPI.Controllers;
using MyDSitefinityAPI.IntegrationTests.Mock;
using System.Text.Json;
using MyDSitefinityAPI.Models;
using Microsoft.AspNetCore.Mvc;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Controllers
{
    [TestClass]
    public class SitefinityFormControllerTests : Test
    {
        private SitefinityFormController Controller { get; }

        public SitefinityFormControllerTests()
        {
            Controller = new SitefinityFormController(new MockLogger<SitefinityFormController>(), RecruitmentWebsiteFormImplentationService);
        }

        [TestMethod]
        public async Task PostQuickContactForm()
        {
            // Arrange
            var json = JsonDocument.Parse(System.IO.File.ReadAllText("GoldenMasters/QuickContactForm.json")).RootElement;

            // Act
            var result = await Controller.PostForm("test", json);

            // Assert
            Assert.IsInstanceOfType(result.Result, typeof(CreatedAtActionResult));
        }

        [TestMethod]
        public async Task PostRecruitmentJobApplicationForm()
        {
            // Arrange
            var json = JsonDocument.Parse(System.IO.File.ReadAllText("GoldenMasters/RecruitmentJobApplication.json")).RootElement;

            // Act
            var result = await Controller.PostForm("test", json);

            // Assert
            Assert.IsInstanceOfType(result.Result, typeof(CreatedAtActionResult));
        }
    }
}
