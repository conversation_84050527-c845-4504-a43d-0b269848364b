using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using MyDSitefinityAPI.Models;
using Swashbuckle.AspNetCore.Filters;
using System.Net;
using Microsoft.AspNetCore.Http;
using System;
using MyDSitefinityAPI.CustomAttributes;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Services;
using MyDSitefinityAPI.ConfigHelpers;

namespace MyDSitefinityAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SitefinityFormController : ControllerBase
    {

        private readonly ILogger<SitefinityFormController> _logger;
        private ISitefinityFormService _sitefinityFormService;

        public SitefinityFormController(ILogger<SitefinityFormController> logger, ISitefinityFormService sitefinityFormService)
        {
            _logger = logger;
            _sitefinityFormService = sitefinityFormService;
        }

        /// <summary>
        /// Sends a sitefinity form to the mydentist backend.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// POST /Sitefinityform
        ///   {
        ///  "Name": "Form is submitted",
        ///  "SiteId": "75c4d617-8e6d-4716-b9eb-a9ef6f2fc2ae",
        ///  "Item": null,
        ///  "OriginalEvent": {
        ///    "EntryId": "cbc0d3b7-1018-475f-be5c-896d5b925c5d",
        ///    "ReferralCode": "1",
        ///    "UserId": "c824f8d5-83fe-478e-8ed0-ade5c30bc884",
        ///    "Username": "<EMAIL>",
        ///    "IpAddress": "127.0.0.1",
        ///    "SubmissionTime": "2022-09-02T10:47:31.3136116Z",
        ///    "FormId": "9289ac17-1a2f-4251-9c11-0ec298b8c45e",
        ///    "FormName": "sf_quickcontactpmform",
        ///    "FormTitle": "TestForm",
        ///    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
        ///    "SendConfirmationEmail": false,
        ///    "Controls": [
        ///      {
        ///        "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox_C004",
        ///        "Value": "<EMAIL>",
        ///        "OldValue": null
        ///      },
        ///      {
        ///        "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox_C003",
        ///        "Value": "07777777779",
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox_C001",
        ///        "Value": null,
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox",
        ///        "Value": "General_Enquiry",
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "HiddenPracticeId",
        ///        "Value": "240=>{my}dentist, Queensway, Bognor Regis",
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox_C011",
        ///        "Value": "Tambo 2",
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox_C010",
        ///        "Value": "Another test",
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "FormTextBox_C015",
        ///        "Value": "20/07/2003",
        ///        "OldValue": null
        ///      },
        ///      {
        ///    "FieldControlName": null,
        ///        "Id": "ab1b06cc-1d4d-40fd-8244-503db911153f",
        ///        "SiblingId": "00000000-0000-0000-0000-000000000000",
        ///        "Text": null,
        ///        "Type": 0,
        ///        "Title": "Random",
        ///        "FieldName": "GAClientId",
        ///        "Value": "1373058472.1613488417",
        ///        "OldValue": null
        ///      }
        ///    ]
        ///  }
        ///}
        ///
        /// </remarks>
        /// <response code="201">Form Created Successfully</response>
        /// <response code="500">Form is not created</response>
        /// <response code="400">Bad JSON formatting</response>

        [HttpPost]
        [ApiKeyAuth]
        [SwaggerRequestExample(typeof(SitefinityForm), typeof(SampleSitefinityForm))]
        public async Task<ActionResult<string>> PostForm([FromHeader(Name = "X-ApiKey")] string header, JsonElement json)
        {
            _logger.LogInformation($"The raw json at endpoint is:  {json.ToString()}");
            JsonSerializerOptions? options = new JsonSerializerOptions() { PropertyNameCaseInsensitive = true };
            dynamic formSubmission;
            if (json.ToString().Contains("sf_recruitmentjobapplication"))
            {
               formSubmission = JsonSerializer.Deserialize<SitefinityFormV2>(json, options);

            }
            else
            {
                formSubmission = JsonSerializer.Deserialize<SitefinityForm>(json, options);
            }

            switch (formSubmission?.OriginalEvent.FormName)
            {
                case "sf_quickcontactpmform":
                    return await _sitefinityFormService.InsertQuickContactForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Created quickContact form") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error creating quickContact form");
                case "sf_feedback":
                    return await _sitefinityFormService.InsertFeedBackForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Created sf_feedback form") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error creating sf_feedback form"); 
                case "sf_feedbackpracticepages":
                    return await _sitefinityFormService.InsertPatientSupportFeedBackForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Created patient support feedback form") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error creating patient support feedback form");
                case "sf_patientreferralform":
                    return await _sitefinityFormService.EmailChosenRecipient(formSubmission, "sf_patientreferralform", json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_patientreferralform") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_patientreferralform");
                case "sf_dentalreferralform":
                    return await _sitefinityFormService.EmailChosenRecipient(formSubmission, "sf_dentalreferralform", json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_dentalreferralform") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_dentalreferralform");
                case "sf_referral_form_patient_self_referral":
                    return await _sitefinityFormService.ProcessReferralForm(formSubmission, json.ToString(), SitefinityFormService.ReferralFormType.PatientReferralForm) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received PatientReferralForm") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing PatientReferralForm");
                case "sf_referral_dental_referral":
                    return await _sitefinityFormService.ProcessReferralForm(formSubmission, json.ToString(), SitefinityFormService.ReferralFormType.ClinicianReferringForm) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received ClinicianReferringForm") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing ClinicianReferringForm");
                case "sf_implantcentrereferral":
                    return await _sitefinityFormService.EmailChosenRecipient(formSubmission, "sf_implantcentrereferral", json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_implantcentrereferral") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_implantcentrereferral");
                case "sf_wifiloginform":
                    return await _sitefinityFormService.ProcessWifiForm(formSubmission,json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_wifiloginform") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_wifiloginform");
                case "sf_marketing_consent":
                    return await _sitefinityFormService.ProcessMarketingConsentForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_marketing_consent") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_marketing_consent");
                case "sf_homepage_marketing_signup_mvc":
                    return await _sitefinityFormService.ProcessHomePageMarketingMVCForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_homepage_marketing_signup_mvc") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_homepage_marketing_signup_mvc");
                case "sf_quick_contact_generic_form":
                    return await _sitefinityFormService.ProcessGeneralInquiryForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received general Enquiry Form") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing General Enquiry form");
                case "sf_emergency_triage_udac":
                    return await _sitefinityFormService.ProcessEmergencyTriage(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received general Enquiry Form") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing General Enquiry form");
                case "sf_recruitmentjobapplication":
                    return await _sitefinityFormService.PassWebApplicationFormData(formSubmission, json.ToString()) == true ?
                         CreatedAtAction(nameof(PostForm), $"Received sf_recruitmentjobapplication") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_recruitmentjobapplication");
                case "sf_507_referral_form":
                    return await _sitefinityFormService.Process507ReferralForm(formSubmission, json.ToString()) == true ?
                                    CreatedAtAction(nameof(PostForm), $"Received sf_507referralform") :
                                    StatusCode(StatusCodes.Status500InternalServerError, "Error processing sf_507referralform");
                default:
                    _logger.LogInformation($"No rules for form of that type");
                    _logger.LogInformation($" Logging only, not processed - The raw JSON is : {json}");

                    return BadRequest($"No rules for form of that type");

            }
        }

        [HttpPost]
        [Route("PostRecruitmentForm")]
        [ApiKeyAuth]
        [SwaggerRequestExample(typeof(SitefinityForm), typeof(RecruitmentSampleSitefinityForm))]
        public async Task<ActionResult<string>> PostRecruitmentForm([FromHeader(Name = "X-ApiKey")] string header, JsonElement json)
        {
            try
            {
                if (ConfigurationHelper.GetValue("RawJsonLogging") == "yes")
                    _logger.LogInformation($" Logging only, not processed - The RecruitmentWebsiteForm JSON is : {json}");

                JsonSerializerOptions? options = new JsonSerializerOptions() { PropertyNameCaseInsensitive = true };
                SitefinityForm? formSubmission = JsonSerializer.Deserialize<SitefinityForm>(json, options);

                int id = await _sitefinityFormService.InsertRecruitmentWebsiteForm(formSubmission, json);

                return Ok($"Recruitment Website Form Successfully Created Id {id}");
            }
            catch(Exception ex)
            {
                _logger.LogError($"Exception when trying to process Recruitment Form. {ex.Message}" , ex);

                return StatusCode(StatusCodes.Status500InternalServerError);
            }

        }

    }
}