{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "None"
      }
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DbContextWarehouse": "Data Source=myd-clarity-sql; Trusted_Connection=True;Initial Catalog=Warehouse; Encrypt=False",
    //"DbContextWarehouse": "Data Source=myd-clarity-sql; Trusted_Connection=True;Initial Catalog=Warehouse; Encrypt=False",
    "DBContextWebsiteConfig": "Data Source=***********;Initial Catalog=WebsiteConfig; User ID=IoMartVacancyApiUser; Password=ttMydentUser-!...!;TrustServerCertificate=True;"
  },
  "ClinicianPortalSettings": {
    "ApiKey": "1137cb2f-891b-46fd-a13c-e4f77e78e3b6",
    "Url": "https://api.mydentist.co.uk/clinicianportal.api.v3.dev/api/"
  },
  "NearestPractice": {
    "BaseUrl": "https://api.mydentist.co.uk/PracticeLocation.API.V1",
    "PracticeLocationApiKey": "119pReciSION1661*eQs"
  },
  //"RecruitmentHub": {
  //  "ApiKey": "6OuOtBGlq75VhLEch5dV1IJmm",
  //  "BaseUrl": "https://apinew.mydentist.co.uk/",
  //  "WebApply": "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1.staging/api/Application/PostWebApplication"
  //},
  "RecruitmentHub": {
    "ApiKey": "11233",
    "BaseUrl": "http://localhost/",
    "WebApply": "http://localhost/RecruitmentHub/api/Application/PostWebApplication"
  },
  "ReferringFormFieldKeyValue": {
    "PreferredTitle": "DropdownListFieldController",
    "PatientAddressLine1": "TextFieldController_1",
    "PatientAddressLine2": "TextFieldController_6",
    "PatientFirstName": "TextFieldController_0",
    "PatientLastName": "TextFieldController",
    "PatientDateOfBirth": "CustomDayMonthYearController",
    "PatientEmail": "TextFieldController_3",
    "PatientTown": "TextFieldController_7",
    "PatientCounty": "TextFieldController_8",
    "PatientPostcode": "TextFieldController_9",
    "PatientNhsNumber": "TextFieldController_4",
    "PatientContactNumber": "TextFieldController_2",
    "PatientInterest": "CheckboxesFieldController",

    "ClinicianAddressLine1": "TextFieldController_10_3",
    "ClinicianAddressLine2": "TextFieldController_10_4",
    "ClinicianFirstName": "TextFieldController_10",
    "ClinicianLastName": "TextFieldController_10_0",
    "ClinicianTown": "TextFieldController_10_5",
    "ClinicianCounty": "TextFieldController_10_6",
    "ClinicianPostcode": "TextFieldController_10_7",
    "ClinicianGdcNumber": "TextFieldController_10_1",
    "PracticeName": "TextFieldController_10_2",
    "IsClinicianReferralUrgent": "MultipleChoiceFieldController_1",
    "IsNhsOrPrivate": "MultipleChoiceFieldController_0",
    "ClinicianReasonForReferral": "CheckboxesFieldController",
    "OtherRelevantMedicalDentalInfo": "NHS number",
    "WasDptRadioTakenLastYear": "MultipleChoiceFieldController",
    "ClinicianToSee": "TextFieldController_11",

    "ReferralSource": "SourcePage",
    "PracticeReferredTo": "PracticeIdTextBox"
  },
  "PracticeClosedRedirects": {
    "922": "112|WA5 2BS",
    "192": "394|G5 0DP",
    "250": "251|E13 8QE",
    "252": "251|E13 8QE",
    "510": "421|PO16 0BG",
    "94": "184|G42 9HN"
  }

}
