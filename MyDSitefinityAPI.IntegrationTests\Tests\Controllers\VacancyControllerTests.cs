using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinityAPI.Controllers;
using MyDSitefinityAPI.IntegrationTests.Mock;
using System.Text.Json;
using MyDSitefinityAPI.Models;
using Microsoft.AspNetCore.Mvc;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Controllers
{
    [TestClass]
    public class VacancyControllerTests : Test
    {
        private VacancyController Controller { get; }

        public VacancyControllerTests()
        {
            Controller = new VacancyController(VacancyService);
        }

        [TestMethod]
        public async Task ApplyToVacancy()
        {
            // Arrange
            var json = JsonDocument.Parse(System.IO.File.ReadAllText("GoldenMasters/VacancyApplication.json")).RootElement;

            // Act
            var result = await Controller.ApplyToVacancy("test", json);

            // Assert
            Assert.IsInstanceOfType(result.Result, typeof(CreatedAtActionResult));
        }
    }
}
