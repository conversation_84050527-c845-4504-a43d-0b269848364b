[{"ContainingType": "MyDSitefinityAPI.Controllers.SitefinityFormController", "Method": "PostForm", "RelativePath": "api/SitefinityForm", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-Api<PERSON>ey", "Type": "System.String", "IsRequired": false}, {"Name": "json", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MyDSitefinityAPI.Controllers.SitefinityFormController", "Method": "PostRecruitmentForm", "RelativePath": "api/SitefinityForm/PostRecruitmentForm", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-Api<PERSON>ey", "Type": "System.String", "IsRequired": false}, {"Name": "json", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MyDSitefinityAPI.Controllers.VacancyController", "Method": "ApplyToVacancy", "RelativePath": "api/Vacancy/ApplyToVacancy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-Api<PERSON>ey", "Type": "System.String", "IsRequired": false}, {"Name": "json", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MyDSitefinityAPI.Controllers.ClinicianApprovalController", "Method": "ApproveClinician", "RelativePath": "workflow/ClinicianApproval/ApproveClinician", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageId", "Type": "System.Guid", "IsRequired": false}, {"Name": "token", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MyDSitefinityAPI.Controllers.ClinicianApprovalController", "Method": "ApproveClinician", "RelativePath": "workflow/ClinicianApproval/ApproveClinician", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "GdcNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "DentalSchoolQualified", "Type": "System.String", "IsRequired": false}, {"Name": "CountryQualified", "Type": "System.String", "IsRequired": false}, {"Name": "YearQualified", "Type": "System.String", "IsRequired": false}, {"Name": "Bio", "Type": "System.String", "IsRequired": false}, {"Name": "Gender", "Type": "System.String", "IsRequired": false}, {"Name": "RejectionNote", "Type": "System.String", "IsRequired": false}, {"Name": "ImageId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OnlinePresenceStatusId", "Type": "System.Int32", "IsRequired": false}, {"Name": "LastEditedBy", "Type": "System.String", "IsRequired": false}, {"Name": "LastEditedDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "FurtherTraining", "Type": "System.String", "IsRequired": false}, {"Name": "GdcSpecialistRegistration", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.GdcNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.Name", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.GdcNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Title", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.DentalSchoolQualified", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.CountryQualified", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.YearQualified", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Bio", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Gender", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.RejectionNote", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.ImageId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Performer.PerformerProfile.OnlinePresenceStatusId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Performer.PerformerProfile.LastEditedBy", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.LastEditedDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Performer.PerformerProfile.FurtherTraining", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.GdcSpecialistRegistration", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Performer.GdcNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Performer.Name", "Type": "System.String", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Performer.PerformerProfile", "Type": "MyDSitefinityAPI.Models.PracticeDirectory.PerformerProfile", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Images", "Type": "System.Collections.Generic.List`1[[MyDSitefinityAPI.Models.PracticeDirectory.PerformerImage, MyDSitefinityAPI, Version=4.0.3.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Performer.PerformerProfile.PracticePeople", "Type": "System.Collections.Generic.List`1[[MyDSitefinityAPI.Models.PracticeDirectory.PracticePerson, MyDSitefinityAPI, Version=4.0.3.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Performer.PerformerProfile.OnlinePresenceStatus", "Type": "MyDSitefinityAPI.Models.PracticeDirectory.OnlinePresenceStatus", "IsRequired": false}, {"Name": "Performer.PerformerProfile.Token", "Type": "System.String", "IsRequired": false}, {"Name": "Images", "Type": "System.Collections.Generic.List`1[[MyDSitefinityAPI.Models.PracticeDirectory.PerformerImage, MyDSitefinityAPI, Version=4.0.3.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "PracticePeople", "Type": "System.Collections.Generic.List`1[[MyDSitefinityAPI.Models.PracticeDirectory.PracticePerson, MyDSitefinityAPI, Version=4.0.3.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "OnlinePresenceStatus", "Type": "MyDSitefinityAPI.Models.PracticeDirectory.OnlinePresenceStatus", "IsRequired": false}, {"Name": "Token", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MyDSitefinityAPI.Controllers.ClinicianApprovalController", "Method": "<PERSON><PERSON>", "RelativePath": "workflow/ClinicianApproval/Login", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageId", "Type": "System.Guid", "IsRequired": false}, {"Name": "token", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MyDSitefinityAPI.Controllers.ClinicianApprovalController", "Method": "<PERSON><PERSON>", "RelativePath": "workflow/ClinicianApproval/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "MyDSitefinityAPI.Models.PublicUser", "IsRequired": true}], "ReturnTypes": []}]